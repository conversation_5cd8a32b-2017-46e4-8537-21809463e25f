"""
Enhanced OCR Service with Automatic Vendor Detection and Improved Line Item Extraction

This service combines vendor detection with optimized OCR processing
to provide better accuracy and automatic routing to vendor-specific APIs.
"""

import os
import logging
from typing import Dict, Any, Optional, List
from .vendor_detection import VendorDetectionService
from .mistral_service import MistralService

logger = logging.getLogger(__name__)

class EnhancedOCRService:
    """
    Enhanced OCR service that automatically detects vendors and optimizes
    line item extraction through post-processing and fallback mechanisms.
    """
    
    def __init__(self):
        """Initialize the enhanced OCR service."""
        self.vendor_detector = VendorDetectionService()
        self.mistral_service = MistralService()

        logger.info("Enhanced OCR service initialized with Mistral OCR")
    
    def process_invoice_with_auto_detection(self, 
                                          image_path: str,
                                          initial_vendor_hint: str = None) -> Dict[str, Any]:
        """
        Process an invoice with automatic vendor detection and enhanced extraction.
        
        Args:
            image_path: Path to the invoice image
            initial_vendor_hint: Optional initial vendor hint
            
        Returns:
            Enhanced OCR result with vendor detection and optimized line items
        """
        try:
            logger.info(f"Processing invoice with auto-detection: {image_path}")
            
            # Step 1: Initial processing with generic API to get basic data
            initial_result = self._get_initial_ocr_data(image_path)
            
            if not initial_result.get("success"):
                return initial_result
            
            # Step 2: Detect vendor from initial OCR data
            detected_vendor, confidence = self._detect_vendor_from_ocr(
                initial_result, initial_vendor_hint
            )
            
            logger.info(f"Detected vendor: {detected_vendor} (confidence: {confidence})")
            
            # Step 3: Re-process with vendor-specific API if confidence is high enough
            final_result = self._process_with_detected_vendor(
                image_path, detected_vendor, confidence, initial_result
            )
            
            # Step 4: Enhance line item extraction
            enhanced_result = self._enhance_line_item_extraction(final_result)
            
            # Step 5: Add vendor detection metadata
            enhanced_result["vendor_detection"] = {
                "detected_vendor": detected_vendor,
                "confidence": confidence,
                "auto_detected": True
            }
            
            return enhanced_result
            
        except Exception as e:
            logger.error(f"Error in enhanced OCR processing: {str(e)}")
            return {
                "success": False,
                "error": f"Enhanced OCR processing failed: {str(e)}",
                "items": []
            }
    
    def _get_initial_ocr_data(self, image_path: str) -> Dict[str, Any]:
        """
        Get initial OCR data using Mistral OCR API.

        Args:
            image_path: Path to the invoice image

        Returns:
            Initial OCR result
        """
        try:
            # Use Mistral OCR service for initial processing
            return self.mistral_service.process_invoice(image_path, 'generic')

        except Exception as e:
            logger.error(f"Error getting initial OCR data: {e}")
            return {
                "success": False,
                "error": str(e),
                "items": []
            }
    
    def _detect_vendor_from_ocr(self, 
                               ocr_result: Dict[str, Any],
                               initial_hint: str = None) -> tuple:
        """
        Detect vendor from OCR result data.
        
        Args:
            ocr_result: Initial OCR result
            initial_hint: Optional initial vendor hint
            
        Returns:
            Tuple of (vendor_id, confidence)
        """
        try:
            # Extract text sources from OCR result
            supplier_name = ocr_result.get("supplier_name")
            invoice_text = ocr_result.get("raw_text", "")
            invoice_number = ocr_result.get("invoice_number")
            
            # Use vendor detection service
            detected_vendor, confidence = self.vendor_detector.detect_vendor(
                supplier_name=supplier_name,
                invoice_text=invoice_text,
                invoice_number=invoice_number
            )
            
            # If we have an initial hint and detection confidence is low, use the hint
            if initial_hint and confidence < 0.7:
                if initial_hint in self.vendor_detector.get_supported_vendors():
                    logger.info(f"Using initial vendor hint: {initial_hint}")
                    return initial_hint, 0.5
            
            return detected_vendor, confidence
            
        except Exception as e:
            logger.error(f"Error in vendor detection: {e}")
            return 'generic', 0.0
    
    def _process_with_detected_vendor(self, 
                                    image_path: str,
                                    vendor: str,
                                    confidence: float,
                                    initial_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process with detected vendor-specific API if confidence is sufficient.
        
        Args:
            image_path: Path to the invoice image
            vendor: Detected vendor ID
            confidence: Detection confidence
            initial_result: Initial OCR result
            
        Returns:
            Final OCR result
        """
        try:
            # Use vendor-specific API if confidence is high enough and vendor is supported
            if confidence >= 0.7 and vendor != 'generic':
                logger.info(f"Re-processing with vendor-specific API: {vendor}")
                
                # Try vendor-specific processing
                vendor_result = self._process_with_vendor_api(image_path, vendor)
                
                if vendor_result.get("success") and len(vendor_result.get("items", [])) > 0:
                    logger.info(f"Vendor-specific processing successful, found {len(vendor_result.get('items', []))} items")
                    return vendor_result
                else:
                    logger.warning(f"Vendor-specific processing failed, using initial result")
            
            return initial_result
            
        except Exception as e:
            logger.error(f"Error processing with detected vendor: {e}")
            return initial_result
    
    def _process_with_vendor_api(self, image_path: str, vendor: str) -> Dict[str, Any]:
        """
        Process with vendor-specific processing using Mistral OCR.

        Args:
            image_path: Path to the invoice image
            vendor: Vendor ID

        Returns:
            Vendor-specific OCR result
        """
        try:
            # Use Mistral OCR service with vendor-specific processing
            return self.mistral_service.process_invoice(image_path, vendor)

        except Exception as e:
            logger.error(f"Error with vendor-specific API: {e}")
            return {
                "success": False,
                "error": str(e),
                "items": []
            }
    
    def _enhance_line_item_extraction(self, ocr_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhance line item extraction through post-processing.
        
        Args:
            ocr_result: OCR result to enhance
            
        Returns:
            Enhanced OCR result
        """
        try:
            if not ocr_result.get("success") or not ocr_result.get("items"):
                return ocr_result
            
            enhanced_items = []
            items = ocr_result.get("items", [])
            
            for item in items:
                enhanced_item = self._enhance_single_item(item)
                enhanced_items.append(enhanced_item)
            
            # Update the result with enhanced items
            enhanced_result = ocr_result.copy()
            enhanced_result["items"] = enhanced_items
            enhanced_result["enhancement_applied"] = True
            
            logger.info(f"Enhanced {len(enhanced_items)} line items")
            return enhanced_result
            
        except Exception as e:
            logger.error(f"Error enhancing line items: {e}")
            return ocr_result
    
    def _enhance_single_item(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhance a single line item.
        
        Args:
            item: Line item to enhance
            
        Returns:
            Enhanced line item
        """
        try:
            enhanced_item = item.copy()
            
            # Clean and enhance description
            if "description" in enhanced_item:
                enhanced_item["description"] = self._clean_description(
                    enhanced_item["description"]
                )
            
            # Validate and clean numeric fields
            for field in ["quantity", "unit_price", "total_price"]:
                if field in enhanced_item:
                    enhanced_item[field] = self._clean_numeric_field(
                        enhanced_item[field]
                    )
            
            # Add confidence score if not present
            if "confidence" not in enhanced_item:
                enhanced_item["confidence"] = self._calculate_item_confidence(enhanced_item)
            
            return enhanced_item
            
        except Exception as e:
            logger.error(f"Error enhancing single item: {e}")
            return item
    
    def _clean_description(self, description: str) -> str:
        """Clean and normalize item description."""
        if not description:
            return ""
        
        # Remove extra whitespace
        cleaned = " ".join(description.split())
        
        # Remove common OCR artifacts
        cleaned = cleaned.replace("||", "")
        cleaned = cleaned.replace("|", "")
        
        return cleaned.strip()
    
    def _clean_numeric_field(self, value: Any) -> Optional[float]:
        """Clean and validate numeric field."""
        if value is None:
            return None
        
        try:
            if isinstance(value, (int, float)):
                return float(value)
            
            if isinstance(value, str):
                # Remove common currency symbols and formatting
                cleaned = value.replace("$", "").replace(",", "").strip()
                if cleaned:
                    return float(cleaned)
            
            return None
        except (ValueError, TypeError):
            return None
    
    def _calculate_item_confidence(self, item: Dict[str, Any]) -> float:
        """Calculate confidence score for a line item."""
        confidence = 0.0
        
        # Check if required fields are present and valid
        if item.get("description"):
            confidence += 0.3
        
        if item.get("quantity") is not None:
            confidence += 0.2
        
        if item.get("unit_price") is not None:
            confidence += 0.25
        
        if item.get("total_price") is not None:
            confidence += 0.25
        
        return min(confidence, 1.0)

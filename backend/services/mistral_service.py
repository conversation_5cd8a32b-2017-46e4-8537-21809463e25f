import os
import requests
import json
import logging
import traceback
import base64
import re
from flask import current_app

# Configure logger
logger = logging.getLogger('ocr')

class MistralService:
    """
    Service for interacting with Mistral AI Document OCR API for invoice processing.
    """

    def __init__(self, api_key=None):
        """
        Initialize the Mistral service with API key.
        """
        self.api_key = api_key or os.environ.get('MISTRAL_API_KEY')
        self.base_url = "https://api.mistral.ai/v1/ocr"
        self.model = "mistral-ocr-latest"

        if not self.api_key:
            logger.warning("Mistral API key not found. Using mock data for development.")

    def process_invoice(self, image_path, vendor='generic'):
        """
        Process an invoice image using Mistral OCR API.

        Args:
            image_path: Path to the invoice image file or file-like object
            vendor: Vendor type (for compatibility with existing code)

        Returns:
            dict: Structured data extracted from the invoice
        """
        if not self.api_key:
            logger.warning("No Mistral API key available. Using mock data.")
            return self._mock_response()

        try:
            logger.info(f"Processing invoice with Mistral OCR API")

            # Read file content
            file_content = self._read_file_content(image_path)
            if file_content is None:
                return {
                    "success": False,
                    "error": "Could not read file content",
                    "items": []
                }

            # Encode file to base64
            base64_content = base64.b64encode(file_content).decode('utf-8')

            # Determine content type
            content_type = self._get_content_type(image_path, file_content)

            # Prepare request payload according to Mistral OCR API format
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }

            payload = {
                "model": self.model,
                "document": {
                    "type": "image_url",
                    "image_url": f"data:{content_type};base64,{base64_content}"
                },
                "include_image_base64": True
            }

            logger.info("Sending request to Mistral OCR API")
            response = requests.post(
                self.base_url,
                headers=headers,
                json=payload,
                timeout=120  # 2 minute timeout for large documents
            )

            if response.status_code == 200:
                result = response.json()
                logger.info("Successfully processed invoice with Mistral OCR API")
                logger.debug(f"Mistral OCR response: {json.dumps(result, indent=2)[:1000]}...")
                
                return self._parse_mistral_response(result, vendor)
            else:
                logger.error(f"Mistral OCR API error: {response.status_code} - {response.text}")
                return {
                    "success": False,
                    "error": f"API error: {response.status_code}",
                    "items": []
                }

        except Exception as e:
            logger.error(f"Error processing invoice with Mistral OCR: {str(e)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {
                "success": False,
                "error": str(e),
                "items": []
            }

    def _read_file_content(self, image_path):
        """
        Read file content from path or file-like object.
        
        Args:
            image_path: File path string or file-like object
            
        Returns:
            bytes: File content or None if error
        """
        try:
            if isinstance(image_path, str):
                # It's a file path
                if not os.path.exists(image_path):
                    logger.error(f"File not found: {image_path}")
                    return None
                
                with open(image_path, 'rb') as file:
                    return file.read()
            else:
                # It's a file-like object
                if hasattr(image_path, 'read'):
                    if hasattr(image_path, 'seek'):
                        image_path.seek(0)
                    return image_path.read()
                else:
                    logger.error("Invalid file object: does not have 'read' method")
                    return None
        except Exception as e:
            logger.error(f"Error reading file: {str(e)}")
            return None

    def _get_content_type(self, image_path, file_content):
        """
        Determine the content type of the file.
        
        Args:
            image_path: File path or file-like object
            file_content: File content bytes
            
        Returns:
            str: MIME type
        """
        # Try to determine from file extension first
        if isinstance(image_path, str):
            ext = os.path.splitext(image_path.lower())[1]
            if ext == '.pdf':
                return 'application/pdf'
            elif ext in ['.jpg', '.jpeg']:
                return 'image/jpeg'
            elif ext == '.png':
                return 'image/png'
            elif ext == '.docx':
                return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            elif ext == '.pptx':
                return 'application/vnd.openxmlformats-officedocument.presentationml.presentation'

        # Fallback: check file header
        if file_content[:4] == b'%PDF':
            return 'application/pdf'
        elif file_content[:3] == b'\xff\xd8\xff':
            return 'image/jpeg'
        elif file_content[:8] == b'\x89PNG\r\n\x1a\n':
            return 'image/png'
        
        # Default to JPEG if uncertain
        return 'image/jpeg'

    def _parse_mistral_response(self, response, vendor):
        """
        Parse the response from Mistral OCR API and extract invoice data.

        Args:
            response: JSON response from Mistral OCR API
            vendor: Vendor type for compatibility

        Returns:
            dict: Structured data in our application's format
        """
        try:
            logger.info("Parsing Mistral OCR response to extract invoice data")

            # Get the text content from Mistral OCR response
            # The response contains pages with markdown content
            text_content = ""

            if 'pages' in response and response['pages']:
                # Extract markdown content from all pages
                pages_content = []
                for page in response['pages']:
                    if 'markdown' in page:
                        pages_content.append(page['markdown'])
                text_content = '\n\n'.join(pages_content)

            # Fallback: try other possible response formats
            if not text_content:
                if 'text' in response:
                    text_content = response['text']
                elif 'choices' in response:
                    choices = response.get('choices', [])
                    if choices and len(choices) > 0:
                        choice = choices[0]
                        if 'message' in choice:
                            text_content = choice['message'].get('content', '')
                        else:
                            text_content = choice.get('text', '')

            if not text_content:
                logger.error("No text content found in Mistral OCR response")
                logger.debug(f"Response structure: {json.dumps(response, indent=2)[:500]}...")
                return {
                    "success": False,
                    "error": "No text content in OCR response",
                    "items": []
                }

            logger.debug(f"Extracted text content length: {len(text_content)}")
            logger.debug(f"First 500 chars: {text_content[:500]}...")

            # Parse the text content to extract invoice fields
            parsed_data = self._extract_invoice_data_from_text(text_content, vendor)

            return parsed_data

        except Exception as e:
            logger.error(f"Error parsing Mistral OCR response: {str(e)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {
                "success": False,
                "error": f"Error parsing response: {str(e)}",
                "items": []
            }

    def _extract_invoice_data_from_text(self, text, vendor):
        """
        Extract structured invoice data from OCR text using pattern matching.
        
        Args:
            text: Raw text from OCR
            vendor: Vendor type
            
        Returns:
            dict: Structured invoice data
        """
        try:
            logger.info(f"Extracting invoice data for vendor: {vendor}")
            
            # Initialize result structure
            result = {
                "success": True,
                "invoice_number": "",
                "date": "",
                "due_date": "",
                "total_amount": 0,
                "supplier_name": "",
                "supplier_address": "",
                "items": []
            }

            # Set vendor-specific supplier names
            vendor_names = {
                'bova': 'Bova',
                'kast': 'Kast',
                'dekalb': 'DeKalb Produce'
            }
            result["supplier_name"] = vendor_names.get(vendor, "")

            # Extract invoice number
            invoice_patterns = [
                r'invoice\s*#?\s*:?\s*([A-Z0-9\-]+)',
                r'inv\s*#?\s*:?\s*([A-Z0-9\-]+)',
                r'document\s*#?\s*:?\s*([A-Z0-9\-]+)',
                r'ref\s*#?\s*:?\s*([A-Z0-9\-]+)',
                r'#\s*([A-Z0-9\-]{3,})'
            ]
            
            for pattern in invoice_patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    result["invoice_number"] = match.group(1).strip()
                    logger.info(f"Found invoice number: {result['invoice_number']}")
                    break

            # Extract dates
            date_patterns = [
                r'(?:invoice\s+date|date|inv\s+date)\s*:?\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})',
                r'(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})',
                r'(\d{4}[\/\-]\d{1,2}[\/\-]\d{1,2})'
            ]
            
            for pattern in date_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                if matches:
                    result["date"] = matches[0].strip()
                    logger.info(f"Found date: {result['date']}")
                    break

            # Extract total amount
            total_patterns = [
                r'(?:total|amount\s+due|please\s+pay|grand\s+total)\s*:?\s*\$?\s*([0-9,]+\.?\d*)',
                r'\$\s*([0-9,]+\.\d{2})\s*(?:total|due)',
                r'([0-9,]+\.\d{2})\s*$'  # Last monetary amount on a line
            ]
            
            for pattern in total_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE | re.MULTILINE)
                if matches:
                    # Take the largest amount found
                    amounts = []
                    for match in matches:
                        try:
                            amount = float(match.replace(',', ''))
                            amounts.append(amount)
                        except ValueError:
                            continue
                    
                    if amounts:
                        result["total_amount"] = max(amounts)
                        logger.info(f"Found total amount: ${result['total_amount']}")
                        break

            # Extract line items
            result["items"] = self._extract_line_items_from_text(text, vendor)
            
            # If no total found, calculate from line items
            if result["total_amount"] == 0 and result["items"]:
                calculated_total = sum(item.get('amount', 0) for item in result["items"])
                if calculated_total > 0:
                    result["total_amount"] = calculated_total
                    logger.info(f"Calculated total from line items: ${result['total_amount']}")

            logger.info(f"Successfully extracted invoice data: {len(result['items'])} items, total: ${result['total_amount']}")
            return result

        except Exception as e:
            logger.error(f"Error extracting invoice data from text: {str(e)}")
            return {
                "success": False,
                "error": f"Error extracting data: {str(e)}",
                "items": []
            }

    def _extract_line_items_from_text(self, text, vendor):
        """
        Extract line items from OCR text.
        
        Args:
            text: Raw text from OCR
            vendor: Vendor type
            
        Returns:
            list: List of line item dictionaries
        """
        try:
            logger.info("Extracting line items from text")
            
            items = []
            lines = text.split('\n')
            
            # Look for table-like structures or patterns that indicate line items
            for i, line in enumerate(lines):
                line = line.strip()
                if not line:
                    continue
                
                # Skip header lines
                if any(header in line.lower() for header in ['description', 'qty', 'quantity', 'price', 'amount', 'total']):
                    continue
                
                # Look for lines with monetary amounts (potential line items)
                money_pattern = r'\$?([0-9,]+\.\d{2})'
                money_matches = re.findall(money_pattern, line)
                
                if money_matches and len(line) > 20:  # Line has money and is substantial
                    item = self._parse_line_item(line, vendor)
                    if item and item['description']:
                        items.append(item)
                        logger.debug(f"Extracted line item: {item['description'][:50]}...")

            # Alternative: Look for structured patterns
            if not items:
                items = self._extract_items_from_structured_text(text, vendor)
            
            logger.info(f"Extracted {len(items)} line items")
            return items

        except Exception as e:
            logger.error(f"Error extracting line items: {str(e)}")
            return []

    def _parse_line_item(self, line, vendor):
        """
        Parse a single line to extract item details.
        
        Args:
            line: Text line to parse
            vendor: Vendor type
            
        Returns:
            dict: Line item data or None
        """
        try:
            # Common patterns for line items
            # Format: description qty unit_price amount
            # or: description amount
            
            # Find all numbers in the line
            number_pattern = r'([0-9,]+(?:\.\d{1,2})?)'
            numbers = [float(n.replace(',', '')) for n in re.findall(number_pattern, line)]
            
            if not numbers:
                return None
            
            # Extract description (usually the beginning of the line, before numbers)
            desc_match = re.match(r'^([A-Za-z\s]+)', line)
            description = desc_match.group(1).strip() if desc_match else "Unknown Item"
            
            # Clean up description
            description = re.sub(r'\s+', ' ', description)
            
            # Determine quantity, unit_price, amount based on number of values
            quantity = 1
            unit_price = 0
            amount = 0
            
            if len(numbers) >= 3:
                # qty, unit_price, amount
                quantity = numbers[0]
                unit_price = numbers[1]
                amount = numbers[2]
            elif len(numbers) == 2:
                # qty, amount or unit_price, amount
                if numbers[0] < 10:  # Likely quantity
                    quantity = numbers[0]
                    amount = numbers[1]
                    unit_price = amount / quantity if quantity > 0 else 0
                else:  # Likely unit_price, amount
                    unit_price = numbers[0]
                    amount = numbers[1]
                    quantity = 1
            elif len(numbers) == 1:
                # Just amount
                amount = numbers[0]
                quantity = 1
                unit_price = amount
            
            return {
                'description': description,
                'quantity': quantity,
                'unit_price': unit_price,
                'amount': amount
            }

        except Exception as e:
            logger.debug(f"Error parsing line item '{line}': {str(e)}")
            return None

    def _extract_items_from_structured_text(self, text, vendor):
        """
        Alternative method to extract items from structured text.
        
        Args:
            text: Raw text from OCR
            vendor: Vendor type
            
        Returns:
            list: List of line items
        """
        try:
            items = []
            
            # Look for markdown tables or similar structures
            lines = text.split('\n')
            
            # Find potential item sections
            in_items_section = False
            for line in lines:
                line = line.strip()
                
                # Markers that indicate start of items section
                if any(marker in line.lower() for marker in ['items', 'products', 'line items', 'description']):
                    in_items_section = True
                    continue
                
                # End markers
                if any(marker in line.lower() for marker in ['subtotal', 'tax', 'total', 'amount due']):
                    in_items_section = False
                    continue
                
                if in_items_section and line:
                    # Try to parse as item
                    item = self._parse_line_item(line, vendor)
                    if item and item['description'] and len(item['description']) > 3:
                        items.append(item)
            
            return items

        except Exception as e:
            logger.error(f"Error extracting structured items: {str(e)}")
            return []

    def _mock_response(self):
        """
        Generate mock data for development without API key.

        Returns:
            dict: Mock structured data
        """
        logger.info("Generating mock Mistral OCR response for development")
        return {
            "success": True,
            "invoice_number": "MISTRAL-12345",
            "date": "2025-06-04",
            "due_date": "2025-07-04",
            "total_amount": 1245.80,
            "supplier_name": "Sample Vendor",
            "supplier_address": "123 Business St, City, State",
            "items": [
                {
                    "description": "Premium Chicken Breast",
                    "quantity": 5,
                    "unit_price": 125.50,
                    "amount": 627.50
                },
                {
                    "description": "Fresh Vegetables Mix",
                    "quantity": 10,
                    "unit_price": 31.85,
                    "amount": 318.50
                },
                {
                    "description": "Cooking Oil",
                    "quantity": 2,
                    "unit_price": 149.90,
                    "amount": 299.80
                }
            ]
        }
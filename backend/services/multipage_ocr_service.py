"""
Multi-Page OCR Service

This service handles OCR processing for multi-page invoices, including:
1. Processing merged PDF documents
2. Aggregating data from multiple pages
3. Vendor-specific multi-page processing rules
4. Updating invoice records with OCR results
"""

import os
import tempfile
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import logging

from services.mistral_service import MistralService
from services.enhanced_ocr_service import EnhancedOCRService
from utils.logger import get_logger

logger = get_logger(__name__)

class MultiPageOCRService:
    """
    Service for processing OCR on multi-page invoice documents.
    """
    
    def __init__(self):
        """Initialize the multi-page OCR service."""
        self.mistral_service = MistralService()
        self.enhanced_ocr_service = EnhancedOCRService()
        
        # Vendor-specific aggregation rules
        self.vendor_aggregation_rules = {
            'bova': self._aggregate_bova_pages,
            'kast': self._aggregate_kast_pages,
            'dekalb': self._aggregate_dekalb_pages,
            'generic': self._aggregate_generic_pages
        }
    
    def process_multipage_invoice(self, invoice_id: str, merged_pdf_path: str, 
                                vendor_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Process OCR for a multi-page invoice.
        
        Args:
            invoice_id: ID of the invoice to process
            merged_pdf_path: Path to the merged PDF file
            vendor_name: Optional vendor name for vendor-specific processing
            
        Returns:
            Dictionary containing OCR processing results
        """
        try:
            logger.info(f"Starting multi-page OCR processing for invoice {invoice_id}")
            
            # Validate inputs
            if not os.path.exists(merged_pdf_path):
                raise ValueError(f"Merged PDF not found: {merged_pdf_path}")
            
            # Detect vendor if not provided
            if not vendor_name:
                vendor_name = self._detect_vendor_from_filename(merged_pdf_path)
            
            vendor_name = vendor_name.lower() if vendor_name else 'generic'
            logger.info(f"Processing with vendor: {vendor_name}")
            
            # Process the merged PDF
            if vendor_name in ['bova', 'kast'] and self._supports_multipage_processing(vendor_name):
                # Use vendor-specific multi-page processing
                ocr_result = self._process_vendor_multipage(merged_pdf_path, vendor_name)
            else:
                # Use generic multi-page processing (split PDF and process pages individually)
                ocr_result = self._process_generic_multipage(merged_pdf_path, vendor_name)
            
            if not ocr_result.get('success'):
                return {
                    'success': False,
                    'error': ocr_result.get('error', 'OCR processing failed'),
                    'invoice_id': invoice_id
                }
            
            # Update invoice record with OCR results
            update_success = self._update_invoice_with_ocr_results(invoice_id, ocr_result)
            
            result = {
                'success': True,
                'invoice_id': invoice_id,
                'vendor_name': vendor_name,
                'ocr_confidence': ocr_result.get('confidence', 0.0),
                'pages_processed': ocr_result.get('pages_processed', 0),
                'line_items_count': len(ocr_result.get('line_items', [])),
                'invoice_data': {
                    'invoice_number': ocr_result.get('invoice_number'),
                    'vendor_name': ocr_result.get('vendor_name'),
                    'invoice_date': ocr_result.get('invoice_date'),
                    'total_amount': ocr_result.get('total_amount'),
                    'line_items': ocr_result.get('line_items', [])
                },
                'database_updated': update_success,
                'processing_method': ocr_result.get('processing_method', 'unknown')
            }
            
            logger.info(f"Multi-page OCR completed for invoice {invoice_id}: {result['line_items_count']} items extracted")
            return result
            
        except Exception as e:
            error_msg = f"Failed to process multi-page OCR for invoice {invoice_id}: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'invoice_id': invoice_id
            }
    
    def _supports_multipage_processing(self, vendor_name: str) -> bool:
        """Check if vendor supports native multi-page processing."""
        # Mistral OCR supports multi-page processing for all vendors
        return True
    
    def _process_vendor_multipage(self, pdf_path: str, vendor_name: str) -> Dict[str, Any]:
        """
        Process multi-page PDF using enhanced vendor-specific API.

        Args:
            pdf_path: Path to the merged PDF
            vendor_name: Vendor name for processing

        Returns:
            OCR processing result
        """
        try:
            logger.info(f"Processing multi-page PDF with Mistral OCR for {vendor_name} vendor")

            # Use Mistral OCR service for multi-page processing
            result = self.mistral_service.process_invoice(pdf_path, vendor_name)

            if result.get('success'):
                result['processing_method'] = f'{vendor_name}_multipage_enhanced'
                # Get page count from multipage metadata if available
                multipage_metadata = result.get('multipage_metadata', {})
                result['pages_processed'] = multipage_metadata.get('estimated_page_count', 1)

                # Add enhanced confidence scoring
                if 'multipage_confidence' in result:
                    result['confidence'] = result['multipage_confidence']
            elif result.get('error') == 'vendor_requires_page_splitting':
                # Vendor doesn't support native multi-page, fall back to splitting
                logger.info(f"Vendor {vendor_name} requires page splitting, falling back to generic processing")
                return self._process_generic_multipage(pdf_path, vendor_name)

            return result

        except Exception as e:
            logger.error(f"Error in enhanced vendor multi-page processing: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _process_generic_multipage(self, pdf_path: str, vendor_name: str) -> Dict[str, Any]:
        """
        Process multi-page PDF by splitting into individual pages and aggregating results.
        
        Args:
            pdf_path: Path to the merged PDF
            vendor_name: Vendor name for processing
            
        Returns:
            Aggregated OCR processing result
        """
        try:
            logger.info(f"Processing multi-page PDF by splitting pages (vendor: {vendor_name})")
            
            # Split PDF into individual pages
            page_paths = self._split_pdf_to_pages(pdf_path)
            
            if not page_paths:
                return {
                    'success': False,
                    'error': 'Failed to split PDF into pages'
                }
            
            logger.info(f"Split PDF into {len(page_paths)} pages")
            
            # Process each page individually
            page_results = []
            for i, page_path in enumerate(page_paths):
                try:
                    logger.info(f"Processing page {i+1}/{len(page_paths)}")
                    
                    # Use enhanced OCR service for better results
                    page_result = self.enhanced_ocr_service.process_invoice_with_auto_detection(
                        page_path, vendor_name
                    )
                    
                    if page_result.get('success'):
                        page_result['page_number'] = i + 1
                        page_results.append(page_result)
                    else:
                        logger.warning(f"Failed to process page {i+1}: {page_result.get('error')}")
                        
                except Exception as e:
                    logger.warning(f"Error processing page {i+1}: {str(e)}")
                    continue
                finally:
                    # Clean up page file
                    try:
                        if os.path.exists(page_path):
                            os.remove(page_path)
                    except:
                        pass
            
            if not page_results:
                return {
                    'success': False,
                    'error': 'No pages could be processed successfully'
                }
            
            # Aggregate results from all pages
            aggregated_result = self._aggregate_page_results(page_results, vendor_name)
            aggregated_result['processing_method'] = f'{vendor_name}_multipage_split'
            aggregated_result['pages_processed'] = len(page_results)
            
            return aggregated_result
            
        except Exception as e:
            logger.error(f"Error in generic multi-page processing: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _split_pdf_to_pages(self, pdf_path: str) -> List[str]:
        """
        Split a PDF into individual page images.
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            List of paths to individual page images
        """
        try:
            # Import PyPDF2 for PDF splitting
            from PyPDF2 import PdfReader, PdfWriter
            from PIL import Image
            import fitz  # PyMuPDF for PDF to image conversion
            
            page_paths = []
            temp_dir = tempfile.mkdtemp()
            
            # Open PDF with PyMuPDF for better image conversion
            pdf_document = fitz.open(pdf_path)
            
            for page_num in range(len(pdf_document)):
                # Convert PDF page to image
                page = pdf_document.load_page(page_num)
                mat = fitz.Matrix(2.0, 2.0)  # 2x zoom for better quality
                pix = page.get_pixmap(matrix=mat)
                
                # Save as PNG
                page_image_path = os.path.join(temp_dir, f"page_{page_num + 1}.png")
                pix.save(page_image_path)
                page_paths.append(page_image_path)
                
                logger.debug(f"Created page image: {page_image_path}")
            
            pdf_document.close()
            return page_paths
            
        except ImportError:
            logger.warning("PyMuPDF not available, falling back to basic PDF splitting")
            return self._split_pdf_basic(pdf_path)
        except Exception as e:
            logger.error(f"Error splitting PDF to pages: {str(e)}")
            return []
    
    def _split_pdf_basic(self, pdf_path: str) -> List[str]:
        """
        Basic PDF splitting using PyPDF2 only (fallback method).
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            List of paths to individual PDF pages
        """
        try:
            from PyPDF2 import PdfReader, PdfWriter
            
            page_paths = []
            temp_dir = tempfile.mkdtemp()
            
            reader = PdfReader(pdf_path)
            
            for page_num, page in enumerate(reader.pages):
                writer = PdfWriter()
                writer.add_page(page)
                
                page_pdf_path = os.path.join(temp_dir, f"page_{page_num + 1}.pdf")
                with open(page_pdf_path, 'wb') as output_file:
                    writer.write(output_file)
                
                page_paths.append(page_pdf_path)
                logger.debug(f"Created page PDF: {page_pdf_path}")
            
            return page_paths
            
        except Exception as e:
            logger.error(f"Error in basic PDF splitting: {str(e)}")
            return []
    
    def _aggregate_page_results(self, page_results: List[Dict[str, Any]], vendor_name: str) -> Dict[str, Any]:
        """
        Aggregate OCR results from multiple pages into a single invoice.
        
        Args:
            page_results: List of OCR results from individual pages
            vendor_name: Vendor name for vendor-specific aggregation
            
        Returns:
            Aggregated OCR result
        """
        try:
            # Use vendor-specific aggregation if available
            aggregation_func = self.vendor_aggregation_rules.get(vendor_name, self._aggregate_generic_pages)
            return aggregation_func(page_results)
            
        except Exception as e:
            logger.error(f"Error aggregating page results: {str(e)}")
            return {
                'success': False,
                'error': f'Failed to aggregate page results: {str(e)}'
            }
    
    def _aggregate_generic_pages(self, page_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generic page aggregation logic."""
        try:
            # Take header info from first page
            first_page = page_results[0]
            
            # Aggregate line items from all pages
            all_line_items = []
            total_confidence = 0
            
            for page_result in page_results:
                page_items = page_result.get('items', [])
                all_line_items.extend(page_items)
                total_confidence += page_result.get('confidence', 0)
            
            # Calculate average confidence
            avg_confidence = total_confidence / len(page_results) if page_results else 0
            
            return {
                'success': True,
                'invoice_number': first_page.get('invoice_number'),
                'vendor_name': first_page.get('supplier_name'),
                'invoice_date': first_page.get('date'),
                'total_amount': first_page.get('total_amount'),
                'line_items': all_line_items,
                'confidence': avg_confidence,
                'raw_text': ' '.join([p.get('raw_text', '') for p in page_results])
            }
            
        except Exception as e:
            logger.error(f"Error in generic page aggregation: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def _aggregate_bova_pages(self, page_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Bova-specific page aggregation logic."""
        try:
            # Bova invoices typically have:
            # Page 1: Header info + some line items
            # Page 2-3: Additional line items

            first_page = page_results[0]
            all_line_items = []

            # Collect all line items from all pages
            for page_result in page_results:
                page_items = page_result.get('items', [])
                # Filter out duplicate headers/footers that might be detected
                filtered_items = [item for item in page_items if self._is_valid_line_item(item)]
                all_line_items.extend(filtered_items)

            # Remove duplicates based on description similarity
            unique_items = self._remove_duplicate_line_items(all_line_items)

            # Calculate total from line items (Bova totals are usually accurate on first page)
            calculated_total = sum(float(item.get('amount', 0) or 0) for item in unique_items)
            header_total = first_page.get('total_amount')

            # Use header total if available and reasonable, otherwise use calculated
            final_total = header_total if header_total and abs(float(header_total) - calculated_total) < calculated_total * 0.1 else calculated_total

            return {
                'success': True,
                'invoice_number': first_page.get('invoice_number'),
                'vendor_name': first_page.get('supplier_name') or 'Bova',
                'invoice_date': first_page.get('date'),
                'total_amount': final_total,
                'line_items': unique_items,
                'confidence': min([p.get('confidence', 0) for p in page_results]),
                'raw_text': ' '.join([p.get('raw_text', '') for p in page_results])
            }

        except Exception as e:
            logger.error(f"Error in Bova page aggregation: {str(e)}")
            return self._aggregate_generic_pages(page_results)

    def _aggregate_kast_pages(self, page_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Kast-specific page aggregation logic."""
        try:
            # Kast invoices are usually single page, but handle multi-page if needed
            first_page = page_results[0]
            all_line_items = []

            for page_result in page_results:
                page_items = page_result.get('items', [])
                all_line_items.extend(page_items)

            return {
                'success': True,
                'invoice_number': first_page.get('invoice_number'),
                'vendor_name': first_page.get('supplier_name') or 'Kast',
                'invoice_date': first_page.get('date'),
                'total_amount': first_page.get('total_amount'),
                'line_items': all_line_items,
                'confidence': sum([p.get('confidence', 0) for p in page_results]) / len(page_results),
                'raw_text': ' '.join([p.get('raw_text', '') for p in page_results])
            }

        except Exception as e:
            logger.error(f"Error in Kast page aggregation: {str(e)}")
            return self._aggregate_generic_pages(page_results)

    def _aggregate_dekalb_pages(self, page_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Dekalb-specific page aggregation logic."""
        try:
            # Dekalb processing - similar to generic but with vendor name
            result = self._aggregate_generic_pages(page_results)
            if result.get('success'):
                result['vendor_name'] = result.get('vendor_name') or 'Dekalb'
            return result

        except Exception as e:
            logger.error(f"Error in Dekalb page aggregation: {str(e)}")
            return self._aggregate_generic_pages(page_results)

    def _is_valid_line_item(self, item: Dict[str, Any]) -> bool:
        """Check if an item is a valid line item (not header/footer)."""
        description = item.get('description', '').lower()

        # Filter out common headers/footers
        invalid_patterns = [
            'total', 'subtotal', 'tax', 'invoice', 'date', 'number',
            'page', 'continued', 'thank you', 'remit to', 'address'
        ]

        return (
            description and
            len(description) > 3 and
            not any(pattern in description for pattern in invalid_patterns) and
            (item.get('quantity') or item.get('amount'))
        )

    def _remove_duplicate_line_items(self, items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate line items based on description similarity."""
        unique_items = []

        for item in items:
            description = item.get('description', '').lower().strip()

            # Check if similar item already exists
            is_duplicate = False
            for existing_item in unique_items:
                existing_desc = existing_item.get('description', '').lower().strip()

                # Simple similarity check
                if description and existing_desc:
                    similarity = self._calculate_similarity(description, existing_desc)
                    if similarity > 0.8:  # 80% similarity threshold
                        is_duplicate = True
                        break

            if not is_duplicate:
                unique_items.append(item)

        return unique_items

    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """Calculate simple similarity between two strings."""
        if not str1 or not str2:
            return 0.0

        # Simple Jaccard similarity
        set1 = set(str1.split())
        set2 = set(str2.split())

        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        return intersection / union if union > 0 else 0.0

    def _detect_vendor_from_filename(self, file_path: str) -> str:
        """Detect vendor from filename or path."""
        filename = os.path.basename(file_path).lower()

        if 'bova' in filename:
            return 'bova'
        elif 'kast' in filename:
            return 'kast'
        elif 'dekalb' in filename:
            return 'dekalb'
        else:
            return 'generic'

    def _update_invoice_with_ocr_results(self, invoice_id: str, ocr_result: Dict[str, Any]) -> bool:
        """
        Update invoice record with OCR results.

        Args:
            invoice_id: ID of the invoice to update
            ocr_result: OCR processing results

        Returns:
            True if successful, False otherwise
        """
        try:
            from app import db
            from models.models import Invoice, LineItem

            # Get invoice
            invoice = db.session.query(Invoice).filter_by(id=invoice_id).first()
            if not invoice:
                logger.error(f"Invoice not found: {invoice_id}")
                return False

            # Update invoice fields
            invoice.invoice_number = ocr_result.get('invoice_number')
            invoice.vendor_name = ocr_result.get('vendor_name')
            invoice.invoice_date = self._parse_date(ocr_result.get('invoice_date'))
            invoice.total_amount = ocr_result.get('total_amount')
            invoice.ocr_text = ocr_result.get('raw_text')
            invoice.ocr_json = ocr_result
            invoice.confidence = ocr_result.get('confidence')
            invoice.status = 'processed'  # Update status to processed

            # Clear existing line items
            db.session.query(LineItem).filter_by(invoice_id=invoice_id).delete()

            # Add new line items
            line_items = ocr_result.get('line_items', [])
            for item_data in line_items:
                line_item = LineItem(
                    description=item_data.get('description'),
                    quantity=item_data.get('quantity'),
                    unit_price=item_data.get('unit_price'),
                    amount=item_data.get('amount'),
                    invoice_id=invoice_id
                )
                db.session.add(line_item)

            # Commit changes
            db.session.commit()

            logger.info(f"Updated invoice {invoice_id} with OCR results: {len(line_items)} line items")
            return True

        except Exception as e:
            logger.error(f"Error updating invoice with OCR results: {str(e)}")
            try:
                db.session.rollback()
            except:
                pass
            return False

    def _parse_date(self, date_str: str) -> Optional[datetime]:
        """Parse date string into datetime object."""
        if not date_str:
            return None

        try:
            # Try common date formats
            from datetime import datetime

            formats = [
                '%Y-%m-%d',
                '%m/%d/%Y',
                '%d/%m/%Y',
                '%Y-%m-%d %H:%M:%S',
                '%m-%d-%Y',
                '%d-%m-%Y'
            ]

            for fmt in formats:
                try:
                    return datetime.strptime(str(date_str), fmt).date()
                except ValueError:
                    continue

            logger.warning(f"Could not parse date: {date_str}")
            return None

        except Exception as e:
            logger.warning(f"Error parsing date {date_str}: {str(e)}")
            return None

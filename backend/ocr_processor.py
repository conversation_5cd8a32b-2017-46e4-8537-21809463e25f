import os
import re
from werkzeug.utils import secure_filename
from google.cloud import vision
import cv2
import numpy as np
from datetime import datetime
from utils.image_preprocessing import enhance_image
from utils.custom_extraction import extract_line_items_simple
from services.mistral_service import MistralService
from services.enhanced_ocr_service import EnhancedOCRService
from flask import current_app
import logging

# --- Configure dedicated file logger for OCR ---
ocr_logger = logging.getLogger('ocr') # Get logger named 'ocr'
ocr_logger.setLevel(logging.DEBUG) # Set level to DEBUG
# Prevent logs from propagating to the root logger (and Flask's default handlers)
ocr_logger.propagate = False
# Create file handler
log_file_path = os.path.join(os.path.dirname(__file__), 'ocr.log')
file_handler = logging.FileHandler(log_file_path)
file_handler.setLevel(logging.DEBUG)
# Create formatter and add it to the handler
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)
# Add the handler to the logger if it doesn't have one already
if not ocr_logger.handlers:
    ocr_logger.addHandler(file_handler)
# --------------------------------------------

class OCRProcessor:
    def __init__(self, upload_folder='uploads'):
        self.upload_folder = upload_folder
        if not os.path.exists(upload_folder):
            os.makedirs(upload_folder)

        # Initialize Google Vision Client (relies on GOOGLE_APPLICATION_CREDENTIALS env var)
        try:
            # The client automatically finds credentials from the environment variable
            self.vision_client = vision.ImageAnnotatorClient()
        except Exception as e:
            # Handle credential loading error (log it, raise it, etc.)
            print(f"Error initializing Google Vision client (check GOOGLE_APPLICATION_CREDENTIALS): {e}")
            self.vision_client = None

        # Initialize Mistral service for invoice processing
        self.mistral_service = MistralService()

        # Initialize enhanced OCR service with auto-detection
        self.enhanced_ocr_service = EnhancedOCRService()

        ocr_logger.info(f"OCRProcessor initialized. Upload folder: {self.upload_folder}")

    def process_invoice_enhanced(self, file, vendor_hint=None):
        """
        Process an invoice image using enhanced OCR with automatic vendor detection

        Args:
            file: The uploaded invoice file
            vendor_hint: Optional vendor hint for detection
        """
        if not file:
            return {"error": "No file provided"}, 400

        # Save the file temporarily
        filename = secure_filename(file.filename)
        filepath = os.path.join(self.upload_folder, filename)
        file.save(filepath)

        try:
            ocr_logger.info(f"Processing invoice with enhanced OCR: {filepath}")

            # Use enhanced OCR service with auto-detection
            result = self.enhanced_ocr_service.process_invoice_with_auto_detection(
                filepath, vendor_hint
            )

            # Clean up
            os.remove(filepath)

            if result.get("success"):
                return {
                    "ocr_text": result.get("raw_text", "Enhanced OCR processing"),
                    "extracted_data": {
                        "invoice_number": result.get("invoice_number"),
                        "date": result.get("date"),
                        "vendor": result.get("supplier_name"),
                        "total_amount": result.get("total_amount"),
                        "items": result.get("items", [])
                    },
                    "vendor_detection": result.get("vendor_detection", {}),
                    "enhancement_applied": result.get("enhancement_applied", False)
                }
            else:
                return {"error": result.get("error", "Enhanced OCR processing failed")}

        except Exception as e:
            # Clean up on error
            if os.path.exists(filepath):
                os.remove(filepath)
            ocr_logger.error(f"Error during enhanced OCR processing: {e}")
            return {"error": str(e)}

    def process_invoice(self, file, vendor='generic'):
        """
        Process an invoice image using OCR (legacy method)

        Args:
            file: The uploaded invoice file
            vendor: The vendor type (bova, kast, dekalb, or generic)
        """
        if not file:
            return {"error": "No file provided"}, 400

        # Save the file temporarily
        filename = secure_filename(file.filename)
        filepath = os.path.join(self.upload_folder, filename)
        file.save(filepath)

        try:
            # First try to process with Mistral OCR for better line item extraction
            ocr_logger.info(f"Processing invoice with Mistral OCR: {filepath} (vendor: {vendor})")
            mistral_result = self.process_with_mistral(filepath, vendor)

            # If Mistral processing was successful and returned line items
            if mistral_result.get("success") and len(mistral_result.get("items", [])) > 0:
                ocr_logger.info(f"Successfully processed with Mistral OCR, found {len(mistral_result.get('items', []))} line items")

                # We still want the raw OCR text from Google Vision for display purposes
                if self.vision_client:
                    try:
                        with open(filepath, 'rb') as image_file:
                            content = image_file.read()

                        image = vision.Image(content=content)
                        response = self.vision_client.document_text_detection(image=image)
                        text = response.full_text_annotation.text
                    except Exception as e:
                        ocr_logger.warning(f"Failed to get raw text from Google Vision: {e}")
                        text = "Raw text extraction failed"
                else:
                    text = "Google Vision client not initialized"

                # Clean up
                os.remove(filepath)

                return {
                    "ocr_text": text,
                    "extracted_data": {
                        "invoice_number": mistral_result.get("invoice_number"),
                        "date": mistral_result.get("date"),
                        "vendor": mistral_result.get("supplier_name"),
                        "total_amount": mistral_result.get("total_amount"),
                        "items": mistral_result.get("items", [])
                    }
                }

            # If Mistral OCR failed or returned no line items, fall back to Google Vision
            ocr_logger.info("Mistral OCR processing failed or returned no line items, falling back to Google Vision")

            if not self.vision_client:
                # Update error message slightly
                return {"error": "Google Vision client not initialized. Check GOOGLE_APPLICATION_CREDENTIALS environment variable."}, 500

            # Process the image
            image = cv2.imread(filepath)
            # Enhance image: deskew, threshold, denoise
            processed = enhance_image(image)

            # Read image content
            with open(filepath, 'rb') as image_file:
                content = image_file.read()

            image = vision.Image(content=content)

            # Perform text detection
            response = self.vision_client.document_text_detection(image=image)

            if response.error.message:
                raise Exception(
                    '{}\nFor more info on error messages, check: '
                    'https://cloud.google.com/apis/design/errors'.format(
                        response.error.message))

            text = response.full_text_annotation.text

            # Extract invoice information using the existing method
            invoice_data = self._extract_invoice_data(text)

            # Clean up
            os.remove(filepath)

            return {
                "ocr_text": text,
                "extracted_data": invoice_data
            }

        except Exception as e:
            # Clean up on error
            if os.path.exists(filepath):
                os.remove(filepath)
            ocr_logger.error(f"Error during OCR processing: {e}")
            return {"error": str(e)}, 500

    def process_with_mistral(self, filepath, vendor='generic'):
        """
        Process an invoice using Mistral OCR API

        Args:
            filepath: Path to the invoice image file
            vendor: The vendor type (bova, kast, dekalb, or generic)
        """
        try:
            ocr_logger.info(f"Sending invoice to Mistral OCR for processing: {filepath} (vendor: {vendor})")
            result = self.mistral_service.process_invoice(filepath, vendor)
            ocr_logger.info(f"Mistral OCR processing result: {result}")
            return result
        except Exception as e:
            ocr_logger.error(f"Error during Mistral OCR processing: {e}")
            return {
                "success": False,
                "error": str(e),
                "items": []
            }

    def _extract_invoice_data(self, text):
        """Extract structured data from OCR text"""
        ocr_logger.info(f"--- Full OCR Text Start ---\n{text}\n--- Full OCR Text End ---") # Log the full text

        data = {
            "invoice_number": None,
            "date": None,
            "vendor": None,
            "total_amount": None,
            "items": []
        }

        # Extract invoice number
        invoice_match = re.search(r'(?:invoice|inv|invoice #|invoice no)[\s:]*([a-z0-9\-]+)',
                                 text, re.IGNORECASE)
        if invoice_match:
            data["invoice_number"] = invoice_match.group(1).strip()

        # Extract date
        date_patterns = [
            r'(?:date|invoice date)[\s:]*((?:\d{1,2}[-/]\d{1,2}[-/]\d{2,4})|(?:\d{2,4}[-/]\d{1,2}[-/]\d{1,2}))',
            r'(?:\d{1,2}\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{2,4})'
        ]

        for pattern in date_patterns:
            date_match = re.search(pattern, text, re.IGNORECASE)
            if date_match:
                date_str = date_match.group(1) if '(' in pattern else date_match.group(0)
                try:
                    # Try to parse the date - this is simplified and may need enhancement
                    # for production use with more date formats
                    data["date"] = date_str.strip()
                    break
                except:
                    pass

        # Extract vendor name - often at the top of the invoice
        # This is simplified - in real scenarios, you might need a database of known vendors
        lines = text.split('\n')
        for i in range(min(5, len(lines))):  # Check first 5 lines
            if lines[i].strip() and not re.search(r'invoice|bill|receipt', lines[i], re.IGNORECASE):
                data["vendor"] = lines[i].strip()
                break

        # Extract total amount
        total_match = re.search(r'(?:total|amount due|balance due|grand total)[\s:]*[$€£]?(\d+[.,]\d+)',
                               text, re.IGNORECASE)
        if total_match:
            data["total_amount"] = float(total_match.group(1).replace(',', ''))

        # Try to extract line items using our custom function
        items = extract_line_items_simple(text)
        data["items"] = items

        ocr_logger.info(f"Extracted {len(items)} line items using custom extraction")

        return data

#!/usr/bin/env python3
"""
Run the Flask app on a different port for testing Mistral OCR
"""

import os
import sys
from app import create_app

def main():
    """Run the test server."""
    
    print("🚀 Starting Mistral OCR Test Server")
    print("=" * 50)
    
    # Create the Flask app
    app = create_app()
    
    # Check if Mistral API key is set
    mistral_key = os.environ.get('MISTRAL_API_KEY')
    if not mistral_key:
        print("❌ MISTRAL_API_KEY not found in environment variables")
        print("Please set your Mistral API key in the .env file")
        return False
    
    print(f"✅ Mistral API key found: {mistral_key[:10]}...")
    print("🌐 Starting server on http://localhost:5002")
    print("📝 You can test OCR by uploading invoices to the web interface")
    print("🔄 Your main app on port 5001 will continue running with <PERSON><PERSON>")
    print("\nPress Ctrl+C to stop the test server")
    print("=" * 50)
    
    try:
        # Run on port 5002 to avoid conflict with existing app
        app.run(host='0.0.0.0', port=5002, debug=True)
    except KeyboardInterrupt:
        print("\n👋 Test server stopped")
        return True
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
Test Mistral OCR with a real invoice file
"""

import os
import sys
import json

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.mistral_service import MistralService

def test_real_invoice(invoice_path):
    """Test Mistral OCR with a real invoice file."""
    
    if not os.path.exists(invoice_path):
        print(f"❌ Invoice file not found: {invoice_path}")
        return False
    
    print(f"🔍 Testing Mistral OCR with: {invoice_path}")
    print("=" * 60)
    
    try:
        # Initialize Mistral service
        mistral_service = MistralService()
        
        if not mistral_service.api_key:
            print("❌ No Mistral API key found. Please set MISTRAL_API_KEY in your .env file.")
            return False
        
        print(f"✅ Using API key: {mistral_service.api_key[:10]}...")
        
        # Process the invoice
        print(f"📤 Processing invoice: {os.path.basename(invoice_path)}")
        result = mistral_service.process_invoice(invoice_path, vendor='generic')
        
        print("\n📊 Mistral OCR Results:")
        print("=" * 40)
        print(f"Success: {result.get('success')}")
        
        if result.get('error'):
            print(f"Error: {result.get('error')}")
            return False
        
        # Display extracted data
        print(f"Invoice Number: {result.get('invoice_number', 'Not found')}")
        print(f"Date: {result.get('date', 'Not found')}")
        print(f"Vendor/Supplier: {result.get('supplier_name', 'Not found')}")
        print(f"Total Amount: {result.get('total_amount', 'Not found')}")
        
        # Display line items
        items = result.get('items', [])
        print(f"\nLine Items Found: {len(items)}")
        
        if items:
            print("\nDetailed Line Items:")
            print("-" * 80)
            for i, item in enumerate(items, 1):
                print(f"{i}. Description: {item.get('description', 'N/A')}")
                print(f"   Quantity: {item.get('quantity', 'N/A')}")
                print(f"   Unit Price: ${item.get('unit_price', 'N/A')}")
                print(f"   Amount: ${item.get('amount', 'N/A')}")
                print()
        
        # Show raw extracted text (first 500 chars)
        if 'raw_text' in result:
            print(f"\nRaw Extracted Text (first 500 chars):")
            print("-" * 50)
            print(result['raw_text'][:500])
            if len(result['raw_text']) > 500:
                print("... (truncated)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error processing invoice: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function to test with real invoice."""
    
    print("🚀 Mistral OCR Real Invoice Test")
    print("=" * 60)
    
    # Check for invoice file argument
    if len(sys.argv) > 1:
        invoice_path = sys.argv[1]
    else:
        # Look for common invoice file names in current directory
        common_names = [
            'invoice.pdf', 'invoice.png', 'invoice.jpg', 'invoice.jpeg',
            'test_invoice.pdf', 'test_invoice.png', 'test_invoice.jpg',
            'sample_invoice.pdf', 'sample_invoice.png', 'sample_invoice.jpg'
        ]
        
        found_file = None
        for name in common_names:
            if os.path.exists(name):
                found_file = name
                break
        
        if found_file:
            invoice_path = found_file
            print(f"📄 Found invoice file: {invoice_path}")
        else:
            print("❌ No invoice file specified and none found automatically.")
            print("\nUsage:")
            print("  python3 test_real_invoice.py <path_to_invoice_file>")
            print("\nOr place one of these files in the current directory:")
            for name in common_names:
                print(f"  - {name}")
            return False
    
    # Test the invoice
    success = test_real_invoice(invoice_path)
    
    if success:
        print("\n🎉 Invoice processing completed successfully!")
        print("\n💡 Tips for comparison:")
        print("- Compare the extracted data with your Mindee results")
        print("- Check accuracy of line item extraction")
        print("- Verify vendor/supplier name recognition")
        print("- Test with different invoice formats")
    else:
        print("\n❌ Invoice processing failed.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

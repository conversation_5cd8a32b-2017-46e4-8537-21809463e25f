#!/usr/bin/env python3
"""
Test script for API endpoints with Mistral OCR
This script tests the Flask API endpoints to ensure they work with Mistral OCR.
"""

import os
import sys
import json
import requests
import tempfile
from pathlib import Path

def create_test_image():
    """Create a simple test image for API testing."""
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # Create a simple invoice image
        width, height = 600, 400
        image = Image.new('RGB', (width, height), 'white')
        draw = ImageDraw.Draw(image)
        
        # Use default font
        try:
            font = ImageFont.load_default()
        except:
            font = None
        
        # Draw simple invoice content
        draw.text((50, 50), "TEST INVOICE", fill='black', font=font)
        draw.text((50, 100), "Invoice #: TEST-001", fill='black', font=font)
        draw.text((50, 130), "Date: 2025-01-15", fill='black', font=font)
        draw.text((50, 160), "Vendor: Test Company", fill='black', font=font)
        draw.text((50, 200), "Item: Test Product - $100.00", fill='black', font=font)
        draw.text((50, 250), "Total: $100.00", fill='black', font=font)
        
        # Save to temporary file
        temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
        image.save(temp_file.name, 'PNG')
        temp_file.close()
        
        return temp_file.name
        
    except ImportError:
        print("PIL not available, cannot create test image")
        return None
    except Exception as e:
        print(f"Error creating test image: {e}")
        return None

def test_health_endpoint(base_url):
    """Test the health endpoint."""
    print("\n🔍 Testing health endpoint...")
    
    try:
        response = requests.get(f"{base_url}/health")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Health endpoint test failed: {e}")
        return False

def test_auth_endpoints(base_url):
    """Test authentication endpoints."""
    print("\n🔍 Testing authentication endpoints...")
    
    try:
        # Test registration
        register_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
        
        response = requests.post(f"{base_url}/auth/register", json=register_data)
        print(f"Register Status Code: {response.status_code}")
        
        if response.status_code in [200, 201]:
            print("✅ Registration successful")
        elif response.status_code == 400 and "already exists" in response.text:
            print("ℹ️  User already exists, continuing with login test")
        else:
            print(f"❌ Registration failed: {response.text}")
            return False, None
        
        # Test login
        login_data = {
            "username": "testuser",
            "password": "testpassword123"
        }
        
        response = requests.post(f"{base_url}/auth/login", json=login_data)
        print(f"Login Status Code: {response.status_code}")
        
        if response.status_code == 200:
            token = response.json().get('access_token')
            print("✅ Login successful")
            return True, token
        else:
            print(f"❌ Login failed: {response.text}")
            return False, None
            
    except Exception as e:
        print(f"❌ Auth endpoints test failed: {e}")
        return False, None

def test_ocr_upload_endpoint(base_url, token):
    """Test the OCR upload endpoint."""
    print("\n🔍 Testing OCR upload endpoint...")
    
    try:
        # Create test image
        test_image_path = create_test_image()
        if not test_image_path:
            print("❌ Could not create test image")
            return False
        
        # Prepare headers
        headers = {
            'Authorization': f'Bearer {token}'
        }
        
        # Upload file
        with open(test_image_path, 'rb') as f:
            files = {'file': ('test_invoice.png', f, 'image/png')}
            response = requests.post(f"{base_url}/ocr/upload-enhanced", 
                                   headers=headers, files=files)
        
        print(f"Upload Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ OCR upload successful")
            print(f"Message: {result.get('message', 'No message')}")
            
            # Check if we got OCR data
            if 'ocr_text' in result:
                print(f"OCR Text Length: {len(result['ocr_text'])}")
            
            if 'extracted_data' in result:
                extracted = result['extracted_data']
                print(f"Invoice Number: {extracted.get('invoice_number', 'None')}")
                print(f"Vendor: {extracted.get('vendor', 'None')}")
                print(f"Total Amount: {extracted.get('total_amount', 'None')}")
                print(f"Line Items: {len(extracted.get('items', []))}")
            
            return True
        else:
            print(f"❌ OCR upload failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ OCR upload test failed: {e}")
        return False
    finally:
        # Clean up test image
        if test_image_path and os.path.exists(test_image_path):
            os.unlink(test_image_path)

def test_ocr_latest_endpoint(base_url, token):
    """Test the latest OCR result endpoint."""
    print("\n🔍 Testing latest OCR result endpoint...")
    
    try:
        headers = {
            'Authorization': f'Bearer {token}'
        }
        
        response = requests.get(f"{base_url}/ocr/latest", headers=headers)
        print(f"Latest OCR Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Latest OCR result retrieved")
            print(f"Message: {result.get('message', 'No message')}")
            return True
        elif response.status_code == 404:
            print("ℹ️  No OCR results found (expected for new user)")
            return True
        else:
            print(f"❌ Latest OCR test failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Latest OCR test failed: {e}")
        return False

def main():
    """Main test function."""
    print("🚀 Starting API Endpoints Test with Mistral OCR")
    print("=" * 60)
    
    # Configuration - using your running app
    base_url = "http://localhost:5001/api/v1"
    
    print(f"Testing API at: {base_url}")
    
    # Test health endpoint first
    if not test_health_endpoint(base_url):
        print("❌ Health check failed. Make sure the server is running.")
        return False
    
    # Test authentication
    auth_success, token = test_auth_endpoints(base_url)
    if not auth_success or not token:
        print("❌ Authentication failed. Cannot proceed with OCR tests.")
        return False
    
    # Test OCR endpoints
    tests_passed = 0
    total_tests = 2
    
    # Test OCR upload
    if test_ocr_upload_endpoint(base_url, token):
        tests_passed += 1
        print("✅ OCR upload test passed")
    else:
        print("❌ OCR upload test failed")
    
    # Test latest OCR
    if test_ocr_latest_endpoint(base_url, token):
        tests_passed += 1
        print("✅ Latest OCR test passed")
    else:
        print("❌ Latest OCR test failed")
    
    # Summary
    print("\n" + "=" * 60)
    print(f"🎯 API Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All API tests passed! Mistral OCR integration is working correctly.")
        return True
    else:
        print("⚠️  Some API tests failed. Please check the error messages above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

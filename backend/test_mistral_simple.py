#!/usr/bin/env python3
"""
Simple test for Mistral OCR integration
"""

import os
import sys
import json
import tempfile
import requests

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_simple_test_image():
    """Create a simple test image."""
    try:
        from PIL import Image, ImageDraw
        
        # Create a simple invoice image
        width, height = 600, 400
        image = Image.new('RGB', (width, height), 'white')
        draw = ImageDraw.Draw(image)
        
        # Draw simple invoice content
        draw.text((50, 50), "TEST INVOICE", fill='black')
        draw.text((50, 100), "Invoice #: TEST-001", fill='black')
        draw.text((50, 130), "Date: 2025-01-15", fill='black')
        draw.text((50, 160), "Vendor: Test Company", fill='black')
        draw.text((50, 200), "Item: Test Product - $100.00", fill='black')
        draw.text((50, 250), "Total: $100.00", fill='black')
        
        # Save to temporary file
        temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
        image.save(temp_file.name, 'PNG')
        temp_file.close()
        
        return temp_file.name
        
    except ImportError:
        print("PIL not available, cannot create test image")
        return None
    except Exception as e:
        print(f"Error creating test image: {e}")
        return None

def test_mistral_api_direct():
    """Test Mistral OCR API directly."""
    print("🔍 Testing Mistral OCR API directly...")
    
    # Use the API key from the .env file in parent directory
    api_key = "SONoPdZEcMvEAhNqh0E0VVt4jHRADHab"
    
    if not api_key:
        print("❌ No API key provided")
        return False
    
    print(f"✅ Using API key: {api_key[:10]}...")
    
    # Create test image
    test_image_path = create_simple_test_image()
    if not test_image_path:
        print("❌ Could not create test image")
        return False
    
    try:
        # Read and encode the image
        with open(test_image_path, 'rb') as f:
            import base64
            image_data = base64.b64encode(f.read()).decode('utf-8')
        
        # Prepare the request
        url = "https://api.mistral.ai/v1/ocr"
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            "model": "mistral-ocr-latest",
            "document": {
                "type": "image_url",
                "image_url": f"data:image/png;base64,{image_data}"
            },
            "include_image_base64": True
        }
        
        print("📤 Sending request to Mistral OCR API...")
        response = requests.post(url, headers=headers, json=payload, timeout=60)
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Mistral OCR API call successful!")
            print(f"Response keys: {list(result.keys())}")
            
            # Try to extract text content from Mistral OCR response
            text_content = ""

            # Check for pages with markdown content (correct format)
            if 'pages' in result and result['pages']:
                pages_content = []
                for page in result['pages']:
                    if 'markdown' in page:
                        pages_content.append(page['markdown'])
                text_content = '\n\n'.join(pages_content)

            # Fallback: try other formats
            if not text_content:
                if 'text' in result:
                    text_content = result['text']
                elif 'choices' in result and result['choices']:
                    choice = result['choices'][0]
                    if 'message' in choice:
                        text_content = choice['message'].get('content', '')
                    else:
                        text_content = choice.get('text', '')

            if text_content:
                print(f"📄 Extracted text (first 200 chars): {text_content[:200]}...")
                return True
            else:
                print("⚠️  No text content found in response")
                print(f"Full response: {json.dumps(result, indent=2)[:500]}...")
                return False
        else:
            print(f"❌ API call failed: {response.status_code}")
            print(f"Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Mistral API: {e}")
        return False
    finally:
        # Clean up
        if test_image_path and os.path.exists(test_image_path):
            os.unlink(test_image_path)

def test_mistral_service():
    """Test the MistralService class."""
    print("\n🔍 Testing MistralService class...")
    
    try:
        from services.mistral_service import MistralService
        
        # Initialize with explicit API key
        mistral_service = MistralService(api_key="SONoPdZEcMvEAhNqh0E0VVt4jHRADHab")
        
        # Create test image
        test_image_path = create_simple_test_image()
        if not test_image_path:
            print("❌ Could not create test image")
            return False
        
        print("📤 Testing MistralService.process_invoice()...")
        result = mistral_service.process_invoice(test_image_path, vendor='generic')
        
        print(f"📊 MistralService result:")
        print(f"  Success: {result.get('success')}")
        print(f"  Error: {result.get('error', 'None')}")
        print(f"  Invoice Number: {result.get('invoice_number', 'None')}")
        print(f"  Vendor: {result.get('supplier_name', 'None')}")
        print(f"  Total Amount: {result.get('total_amount', 'None')}")
        print(f"  Line Items: {len(result.get('items', []))}")
        
        if result.get('items'):
            print(f"  First Item: {result['items'][0]}")
        
        # Clean up
        os.unlink(test_image_path)
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ Error testing MistralService: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🚀 Simple Mistral OCR Test")
    print("=" * 40)
    
    tests_passed = 0
    total_tests = 2
    
    # Test 1: Direct API call
    if test_mistral_api_direct():
        tests_passed += 1
        print("✅ Direct API test passed")
    else:
        print("❌ Direct API test failed")
    
    # Test 2: MistralService class
    if test_mistral_service():
        tests_passed += 1
        print("✅ MistralService test passed")
    else:
        print("❌ MistralService test failed")
    
    # Summary
    print("\n" + "=" * 40)
    print(f"🎯 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Mistral OCR is working!")
        return True
    else:
        print("⚠️  Some tests failed.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
